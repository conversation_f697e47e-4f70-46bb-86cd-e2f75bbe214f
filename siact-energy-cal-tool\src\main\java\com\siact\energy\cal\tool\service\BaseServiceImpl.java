package com.siact.energy.cal.tool.service;

import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.tool.common.config.AuthProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class BaseServiceImpl implements BaseService {
    private static final Logger logger = LoggerFactory.getLogger(BaseServiceImpl.class);

    @Autowired(required = false)
    private Object authService; // 使用Object类型避免编译时依赖

    @Autowired
    private AuthProperties authProperties;

    @Override
    public boolean isEnabled() {
        return authProperties.isEnabled();
    }

    @Override
    public Long getCurrentUserId() {
        if (!isEnabled()) {
            logger.debug("权限系统未启用，返回null用户ID");
            return null;
        }
        if (authService == null) {
            logger.warn("AuthService未注入，权限系统可能未正确配置");
            return null;
        }
        try {
            // 通过反射调用authService.getLoginUser()
            java.lang.reflect.Method getLoginUserMethod = authService.getClass().getMethod("getLoginUser");
            Object userDetailVO = getLoginUserMethod.invoke(authService);

            if (userDetailVO == null) {
                logger.debug("未获取到登录用户信息");
                return null;
            }

            // 通过反射提取用户ID
            return extractUserId(userDetailVO);
        } catch (Exception e) {
            logger.warn("获取登录用户信息失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从UserDetailVO对象中提取用户ID
     */
    private Long extractUserId(Object userDetailVO) {
        try {
            // 尝试常见的用户ID getter方法名
            String[] possibleMethodNames = {"getId", "getUserId", "getUser_id", "getID", "getUSER_ID"};

            for (String methodName : possibleMethodNames) {
                try {
                    java.lang.reflect.Method method = userDetailVO.getClass().getMethod(methodName);
                    Object result = method.invoke(userDetailVO);
                    if (result != null) {
                        if (result instanceof Long) {
                            return (Long) result;
                        } else if (result instanceof Integer) {
                            return ((Integer) result).longValue();
                        } else if (result instanceof String) {
                            return Long.valueOf((String) result);
                        }
                    }
                } catch (Exception ignored) {
                    // 继续尝试下一个方法
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Object> getUserProjects() {
        if (!isEnabled()) {
            return Collections.emptyList();
        }
        if (authService == null) {
            logger.warn("AuthService未注入，权限系统可能未正确配置");
            return Collections.emptyList();
        }
        try {
            // 通过反射调用authService.getUserProjects()
            java.lang.reflect.Method method = authService.getClass().getMethod("getUserProjects");
            Object result = method.invoke(authService);
            if (result instanceof List) {
                return (List<Object>) result;
            }
            return Collections.emptyList();
        } catch (Exception e) {
            logger.warn("获取用户项目权限失败", e);
            return Collections.emptyList(); // 不抛异常，返回空列表让调用方处理
        }
    }

    @Override
    public <T> List<T> filterAuthorizedProjects(List<T> allProjects, Function<T, String> getDataCode) {
        if (!isEnabled()) {
            return allProjects;
        }

        List<String> authorizedProjectCodes = getUserProjects().stream()
                .map(this::extractDataCode) // 使用反射提取dataCode
                .filter(code -> code != null)
                .collect(Collectors.toList());

        if (authorizedProjectCodes.isEmpty()) {
            logger.warn("用户没有任何项目权限");
            return Collections.emptyList(); // 不抛异常，返回空列表
        }

        return allProjects.stream()
                .filter(project -> authorizedProjectCodes.contains(getDataCode.apply(project)))
                .collect(Collectors.toList());
    }

    /**
     * 从项目对象中提取dataCode
     */
    private String extractDataCode(Object project) {
        try {
            // 尝试常见的dataCode getter方法名
            String[] possibleMethodNames = {"getDataCode", "getProjectCode", "getCode"};

            for (String methodName : possibleMethodNames) {
                try {
                    java.lang.reflect.Method method = project.getClass().getMethod(methodName);
                    Object result = method.invoke(project);
                    if (result instanceof String) {
                        return (String) result;
                    }
                } catch (Exception ignored) {
                    // 继续尝试下一个方法
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
} 