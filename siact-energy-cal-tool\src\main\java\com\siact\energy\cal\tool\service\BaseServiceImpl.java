package com.siact.energy.cal.tool.service;

import com.siact.auth.server.common.vo.UserDetailVO;
import com.siact.auth.service.AuthService;
import com.siact.auth.vo.ProjectVO;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.tool.common.config.AuthProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class BaseServiceImpl implements BaseService {
    private static final Logger logger = LoggerFactory.getLogger(BaseServiceImpl.class);

    @Autowired
    private AuthService authService;

    @Autowired
    private AuthProperties authProperties;

    @Override
    public boolean isEnabled() {
        return authProperties.isEnabled();
    }

    @Override
    public UserDetailVO getLoginUser() {
        if (!isEnabled()) {
            return null;
        }
        try {
            return authService.getLoginUser();
        } catch (Exception e) {
            logger.warn("获取登录用户信息失败", e);
            throw new BizException("获取用户信息失败", e);
        }
    }

    @Override
    public Long getLoginUserId() {
        UserDetailVO loginUser = getLoginUser();
        if(loginUser!=null){
            return loginUser.getId();
        }
        return null;
    }

    @Override
    public List<ProjectVO> getUserProjects() {
        if (!isEnabled()) {
            return Collections.emptyList();
        }
        try {
            return authService.getUserProjects();
        } catch (Exception e) {
            logger.warn("获取用户项目权限失败", e);
            throw new BizException("获取项目权限失败", e);
        }
    }

    @Override
    public <T> List<T> filterAuthorizedProjects(List<T> allProjects, Function<T, String> getDataCode) {
        if (!isEnabled()) {
            return allProjects;
        }

        List<String> authorizedProjectCodes = getUserProjects().stream()
                .map(ProjectVO::getDataCode)
                .collect(Collectors.toList());

        if (authorizedProjectCodes.isEmpty()) {
            throw new BizException("用户没有任何项目权限");
        }

        return allProjects.stream()
                .filter(project -> authorizedProjectCodes.contains(getDataCode.apply(project)))
                .collect(Collectors.toList());
    }

} 