package com.siact.edu.common.pojo.dto.calc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 计算驱动详情DTO
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@ApiModel("计算驱动详情DTO")
@Data
public class CalcDriverDetailDTO {

    /**
     * 驱动id
     */
    @ApiModelProperty(value = "驱动id")
    private String driverId;

    /**
     * 驱动版本
     */
    @ApiModelProperty(value = "驱动版本")
    private String driverVersion;

    /**
     * 驱动编码
     */
    @ApiModelProperty(value = "驱动编码")
    private String driverCode;

    /**
     * 节点类型，系统：system；站点：station；设备：eq
     */
    @ApiModelProperty(value = "节点类型，系统：system；站点：station；设备：eq", required = true)
    @NotBlank(message = "节点类型不能为空")
    private String nodeType;

    /**
     * 节点编码（数字孪生模型长码）
     */
    @ApiModelProperty(value = "节点编码（数字孪生模型长码）", required = true)
    @NotBlank(message = "节点编码不能为空")
    private String nodeCode;

    /**
     * 节点名称（数字孪生模型名称）
     */
    @ApiModelProperty(value = "节点名称（数字孪生模型名称）", required = true)
    @NotBlank(message = "节点名称不能为空")
    private String nodeName;

    /**
     * 驱动数据列表
     */
    @ApiModelProperty(value = "驱动数据列表", required = true)
    @NotEmpty(message = "驱动数据列表不能为空")
    private List<CalcPropDataDTO> driverData;

    /**
     * 父节点数字孪生模型长码，数组。系统：无，站点：系统模型编码，设备：站点模型编码，系统模型编码
     */
    @ApiModelProperty(value = "父节点数字孪生模型长码数组")
    private List<String> parentCode;

    /**
     * 父节点数字孪生模型名称，数组。系统：无，站点：系统模型名称，设备：站点模型名称，系统模型名称
     */
    @ApiModelProperty(value = "父节点数字孪生模型名称数组")
    private List<String> parentName;
}
