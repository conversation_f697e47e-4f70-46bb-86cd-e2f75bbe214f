package com.siact.energy.cal.tool.service;

import java.util.List;
import java.util.function.Function;

public interface BaseService {
    /**
     * 获取当前登录用户ID
     * 当权限系统关闭时返回null
     */
    Long getCurrentUserId();

    /**
     * 获取用户项目权限
     */
    List<Object> getUserProjects();

    /**
     * 根据项目dataCode过滤数据
     */
    <T> List<T> filterAuthorizedProjects(List<T> allProjects, Function<T, String> getDataCode);

    /**
     * 是否启用权限验证
     */
    boolean isEnabled();
}