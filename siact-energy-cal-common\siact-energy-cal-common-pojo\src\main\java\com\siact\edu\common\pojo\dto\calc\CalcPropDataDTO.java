package com.siact.edu.common.pojo.dto.calc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 计算属性数据DTO
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@ApiModel("计算属性数据DTO")
@Data
public class CalcPropDataDTO {

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称", required = true)
    @NotBlank(message = "指标名称不能为空")
    private String ruleName;

    /**
     * 指标描述
     */
    @ApiModelProperty(value = "指标描述")
    private String ruleDes;

    /**
     * 计算类型，字符串格式
     */
    @ApiModelProperty(value = "计算类型，字符串格式", required = true)
    @NotBlank(message = "计算类型不能为空")
    private String calType;

    /**
     * 属性数字孪生模型长码
     */
    @ApiModelProperty(value = "属性数字孪生模型长码", required = true)
    @NotBlank(message = "属性数字孪生模型长码不能为空")
    private String devProperty;

    /**
     * 属性数字孪生模型名称
     */
    @ApiModelProperty(value = "属性数字孪生模型名称", required = true)
    @NotBlank(message = "属性数字孪生模型名称不能为空")
    private String propName;

    /**
     * 公式表达式，数组
     */
    @ApiModelProperty(value = "公式表达式，数组", required = true)
    @NotEmpty(message = "公式表达式不能为空")
    private List<String> ruleFormula;

    /**
     * 公式表达式显示，数组
     */
    @ApiModelProperty(value = "公式表达式显示，数组", required = true)
    @NotEmpty(message = "公式表达式显示不能为空")
    private List<String> ruleFormulaShow;
}
