package com.siact.energy.cal.tool.common.utils;

import com.siact.energy.cal.tool.service.BaseService;

/**
 * 用户上下文工具类
 * 用于获取当前登录用户信息，支持权限系统开启/关闭的场景
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public class UserContextUtil {

    /**
     * 获取当前登录用户ID
     * 当权限系统关闭时，返回null
     *
     * @param baseService BaseService实例
     * @return 当前用户ID，权限系统关闭时返回null
     */
    public static Long getCurrentUserId(BaseService baseService) {
        return baseService.getCurrentUserId();
    }
}
