package com.siact.energy.cal.tool.service;

import java.util.List;
import java.util.function.Function;

public interface BaseService {
    /**
     * 获取当前登录用户
     * 返回Object类型避免编译时对UserDetailVO的强依赖
     */
    Object getLoginUser();

    /**
     * 获取用户项目权限
     * 返回Object类型避免编译时对ProjectVO的强依赖
     */
    List<Object> getUserProjects();
    
    /**
     * 根据项目dataCode过滤数据
     */
    <T> List<T> filterAuthorizedProjects(List<T> allProjects, Function<T, String> getDataCode);
    
    /**
     * 是否启用权限验证
     */
    boolean isEnabled();
}